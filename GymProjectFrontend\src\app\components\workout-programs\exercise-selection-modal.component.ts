import { Component, Inject, OnInit, OnDestroy } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Subject } from 'rxjs';
import { debounceTime, takeUntil } from 'rxjs/operators';
import { ToastrService } from 'ngx-toastr';
import {
  faSearch,
  faTimes,
  faCheck,
  faFilter,
  faList,
  faChevronDown,
  faSignal,
  faTag,
  faDesktop,
  faBuilding,
  faInfoCircle,
  faUser,
  faCog,
  faChevronLeft,
  faChevronRight
} from '@fortawesome/free-solid-svg-icons';

import { SystemExerciseService, SystemExercise } from '../../services/system-exercise.service';
import { CompanyExerciseService, CombinedExercise } from '../../services/company-exercise.service';
import { ExerciseCategoryService, ExerciseCategory } from '../../services/exercise-category.service';

export interface ExerciseSelectionModalData {
  selectedExercise?: {
    exerciseType: string;
    exerciseID: number;
    exerciseName: string;
  };
}

export interface SelectedExerciseResult {
  exerciseType: string;
  exerciseID: number;
  exerciseName: string;
  exerciseDescription?: string;
  categoryName?: string;
}

@Component({
  selector: 'app-exercise-selection-modal',
  templateUrl: './exercise-selection-modal.component.html',
  styleUrls: ['./exercise-selection-modal.component.css'],
  standalone: false
})
export class ExerciseSelectionModalComponent implements OnInit, OnDestroy {
  // Icons
  faSearch = faSearch;
  faTimes = faTimes;
  faCheck = faCheck;
  faFilter = faFilter;
  faList = faList;
  faChevronDown = faChevronDown;
  faSignal = faSignal;
  faTag = faTag;
  faDesktop = faDesktop;
  faBuilding = faBuilding;
  faInfoCircle = faInfoCircle;
  faUser = faUser;
  faCog = faCog;
  faChevronLeft = faChevronLeft;
  faChevronRight = faChevronRight;

  // Data
  exercises: CombinedExercise[] = [];
  categories: ExerciseCategory[] = [];
  
  // Pagination
  currentPage = 1;
  totalPages = 0;
  totalItems = 0;
  pageSize = 15;

  // Filters
  selectedCategoryId: number | string | null = null;
  selectedDifficultyLevel: number | string | null = null;
  searchText = '';
  
  // UI State
  isLoading = false;
  selectedExercise: SelectedExerciseResult | null = null;

  private searchSubject = new Subject<string>();
  private destroy$ = new Subject<void>();

  constructor(
    private systemExerciseService: SystemExerciseService,
    private companyExerciseService: CompanyExerciseService,
    private exerciseCategoryService: ExerciseCategoryService,
    private toastrService: ToastrService,
    public dialogRef: MatDialogRef<ExerciseSelectionModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ExerciseSelectionModalData
  ) {
    this.searchSubject.pipe(
      debounceTime(750),
      takeUntil(this.destroy$)
    ).subscribe(searchValue => {
      this.searchText = searchValue;
      this.currentPage = 1;
      this.loadExercises();
    });
  }

  ngOnInit(): void {
    this.loadCategories();
    this.loadExercises();
    
    // Eğer önceden seçilmiş egzersiz varsa
    if (this.data.selectedExercise) {
      this.selectedExercise = {
        exerciseType: this.data.selectedExercise.exerciseType,
        exerciseID: this.data.selectedExercise.exerciseID,
        exerciseName: this.data.selectedExercise.exerciseName
      };
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadCategories(): void {
    this.exerciseCategoryService.getActiveCategories().subscribe({
      next: (response) => {
        if (response.success) {
          // Kategorileri istenen sıraya göre sırala
          const categoryOrder = ['Göğüs', 'Sırt', 'Kol', 'Omuz', 'Bacak', 'Karın', 'Cardio', 'Functional'];
          this.categories = response.data.sort((a, b) => {
            const indexA = categoryOrder.indexOf(a.categoryName);
            const indexB = categoryOrder.indexOf(b.categoryName);

            if (indexA === -1) return 1;
            if (indexB === -1) return -1;

            return indexA - indexB;
          });
        }
      },
      error: (error) => {
        console.error('Error loading categories:', error);
        this.toastrService.error('Kategoriler yüklenirken hata oluştu', 'Hata');
      }
    });
  }

  loadExercises(): void {
    this.isLoading = true;

    const filter = {
      exerciseCategoryID: (this.selectedCategoryId && this.selectedCategoryId !== '') ? Number(this.selectedCategoryId) : undefined,
      searchTerm: this.searchText || undefined,
      difficultyLevel: (this.selectedDifficultyLevel && this.selectedDifficultyLevel !== '') ? Number(this.selectedDifficultyLevel) : undefined,
      page: this.currentPage,
      pageSize: this.pageSize
    };

    this.companyExerciseService.getCombinedFiltered(filter).subscribe({
      next: (response) => {
        if (response.success) {
          this.exercises = response.data.data;
          this.totalPages = response.data.totalPages;
          this.totalItems = response.data.totalCount;
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading exercises:', error);
        this.toastrService.error('Egzersizler yüklenirken hata oluştu', 'Hata');
        this.isLoading = false;
      }
    });
  }

  onSearch(event: any): void {
    const searchValue = event.target.value;
    this.searchSubject.next(searchValue);
  }

  onCategoryChange(): void {
    this.currentPage = 1;
    this.loadExercises();
  }

  onDifficultyChange(): void {
    this.currentPage = 1;
    this.loadExercises();
  }

  hasActiveFilters(): boolean {
    return !!(this.searchText ||
              (this.selectedCategoryId && this.selectedCategoryId !== '') ||
              (this.selectedDifficultyLevel && this.selectedDifficultyLevel !== ''));
  }

  clearFilters(): void {
    this.selectedCategoryId = null;
    this.selectedDifficultyLevel = null;
    this.searchText = '';
    this.currentPage = 1;
    this.loadExercises();
  }

  selectExercise(exercise: CombinedExercise): void {
    this.selectedExercise = {
      exerciseType: exercise.exerciseType,
      exerciseID: exercise.exerciseID,
      exerciseName: exercise.exerciseName,
      exerciseDescription: exercise.description,
      categoryName: exercise.categoryName
    };
  }

  isExerciseSelected(exercise: CombinedExercise): boolean {
    return this.selectedExercise?.exerciseType === exercise.exerciseType &&
           this.selectedExercise?.exerciseID === exercise.exerciseID;
  }

  onConfirm(): void {
    if (this.selectedExercise) {
      this.dialogRef.close(this.selectedExercise);
    } else {
      this.toastrService.warning('Lütfen bir egzersiz seçin', 'Uyarı');
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      this.loadExercises();
    }
  }

  getPaginationRange(): number[] {
    const range = [];
    const maxPagesToShow = 5;

    let startPage = Math.max(1, this.currentPage - Math.floor(maxPagesToShow / 2));
    let endPage = Math.min(this.totalPages, startPage + maxPagesToShow - 1);

    if (endPage - startPage + 1 < maxPagesToShow) {
      startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      range.push(i);
    }

    return range;
  }

  getDifficultyBadgeClass(level?: number): string {
    switch (level) {
      case 1: return 'modern-badge-success';
      case 2: return 'modern-badge-warning';
      case 3: return 'modern-badge-danger';
      default: return 'modern-badge-secondary';
    }
  }

  getExerciseTypeBadgeClass(type: string): string {
    return type === 'System' ? 'modern-badge-primary' : 'modern-badge-info';
  }

  getDifficultyText(level?: number): string {
    switch (level) {
      case 1: return 'Başlangıç';
      case 2: return 'Orta';
      case 3: return 'İleri';
      default: return '-';
    }
  }

  getDifficultyIcon(level?: number): string {
    switch (level) {
      case 1: return '🟢';
      case 2: return '🟡';
      case 3: return '🔴';
      default: return '⚪';
    }
  }

  clearSearch(): void {
    this.searchText = '';
    this.currentPage = 1;
    this.loadExercises();
  }
}
